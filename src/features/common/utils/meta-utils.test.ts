/**
 * Tests for meta-utils.ts
 * These tests verify that social media meta tags are set correctly
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setIdeaMetaTags, setSolutionMetaTags, clearSocialMediaTags } from './meta-utils';

// Mock DOM methods
const mockSetAttribute = vi.fn();
const mockAppendChild = vi.fn();
const mockQuerySelector = vi.fn();
const mockCreateElement = vi.fn(() => ({
  setAttribute: mockSetAttribute
}));

// Mock document
Object.defineProperty(global, 'document', {
  value: {
    querySelector: mockQuerySelector,
    createElement: mockCreateElement,
    head: {
      appendChild: mockAppendChild
    },
    title: ''
  },
  writable: true
});

// Mock window
Object.defineProperty(global, 'window', {
  value: {
    location: {
      origin: 'https://test.updraft.fund'
    }
  },
  writable: true
});

describe('meta-utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockQuerySelector.mockReturnValue(null); // No existing meta tags
  });

  describe('setIdeaMetaTags', () => {
    it('should set meta tags for an idea with description', () => {
      const ideaData = {
        id: '0x123',
        name: 'Test Idea',
        description: 'This is a test idea description',
        creator: {
          id: '0xabc',
          profile: '0x7b226e616d65223a2254657374205573657222' // hex encoded {"name":"Test User"}
        }
      };

      setIdeaMetaTags(ideaData);

      // Verify document title is set
      expect(document.title).toBe('Test Idea | Updraft');

      // Verify meta tags are created
      expect(mockCreateElement).toHaveBeenCalledWith('meta');
      expect(mockSetAttribute).toHaveBeenCalledWith('property', 'og:title');
      expect(mockSetAttribute).toHaveBeenCalledWith('content', 'Test Idea | Updraft');
    });

    it('should handle undefined description', () => {
      const ideaData = {
        id: '0x123',
        name: 'Test Idea',
        description: undefined,
        creator: {
          id: '0xabc'
        }
      };

      setIdeaMetaTags(ideaData);

      // Should not throw and should set title
      expect(document.title).toBe('Test Idea | Updraft');
    });

    it('should handle null description', () => {
      const ideaData = {
        id: '0x123',
        name: 'Test Idea',
        description: null,
        creator: {
          id: '0xabc'
        }
      };

      setIdeaMetaTags(ideaData);

      // Should not throw and should set title
      expect(document.title).toBe('Test Idea | Updraft');
    });

    it('should handle empty description', () => {
      const ideaData = {
        id: '0x123',
        name: 'Test Idea',
        description: '',
        creator: {
          id: '0xabc'
        }
      };

      setIdeaMetaTags(ideaData);

      // Should not throw and should set title
      expect(document.title).toBe('Test Idea | Updraft');
    });
  });

  describe('setSolutionMetaTags', () => {
    it('should set meta tags for a solution', () => {
      const solutionData = {
        id: '0x456',
        info: '0x7b226e616d65223a22546573742053616c7574696f6e222c226465736372697074696f6e223a2254657374206465736372697074696f6e227d', // hex encoded {"name":"Test Solution","description":"Test description"}
        drafter: {
          id: '0xdef',
          profile: '0x7b226e616d65223a22546573742044726166746572227d' // hex encoded {"name":"Test Drafter"}
        },
        idea: {
          id: '0x123',
          name: 'Test Idea'
        }
      };

      setSolutionMetaTags(solutionData);

      // Verify document title is set
      expect(document.title).toBe('Test Solution | Updraft');
    });

    it('should handle missing solution info', () => {
      const solutionData = {
        id: '0x456',
        drafter: {
          id: '0xdef'
        }
      };

      setSolutionMetaTags(solutionData);

      // Should not throw and should set title with default
      expect(document.title).toBe('Untitled Solution | Updraft');
    });
  });

  describe('clearSocialMediaTags', () => {
    it('should reset meta tags to defaults', () => {
      clearSocialMediaTags();

      // Verify default title is set
      expect(document.title).toBe('Updraft');
    });
  });
});
